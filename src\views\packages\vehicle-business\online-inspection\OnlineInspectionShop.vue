<style lang="scss">
// vue-loader的bug
// 某些情况下，某些元素的scopid没有添加到元素上，导致scop样式不生效
$border-color-gray: #e8e8e8;
.shop-inspection {
  .shop-info {
    background: white;
    border-bottom: 1px solid $border-color-gray;
  }
  .banners {
    height: 200px;
    background: #e4e4e4;
  }
  .cmt-panel {
    .panel-title {
      padding-left: 10px;
    }
  }
  .cmt-st {
    border-bottom: 0;
  }
}
.share {
  width: 45px;
  height: 45px;
  text-align: center;
  line-height: 46px;
  .icon_jglh {
    font-size: 24px;
  }
}
</style>
<style lang="scss" scoped src="../styles/common-678.scss"></style>
<style lang="scss" scoped>
@import '~styles/mixin/index.scss';
$border-color-gray: #e8e8e8;
.shop-detail {
  background: #f3f3f3;
}
.vip-banner {
  height: 40px;
}

.shop-inspection ::v-deep {
  background: #f3f3f3;
  .tabs {
    border-radius: 10px 10px 0 0;
    border-bottom: 1px solid rgb(245, 245, 245);
  }
  .tab-items {
    border: none;
    .tab-item-active {
      color: #fd4925;
      &::after {
        display: none;
      }
    }
  }
  .tab-items > .tab-items-inner {
    display: block;
  }
  .tab-item {
    flex: none;
    padding: 15px 16px;
    font-weight: 700;
    color: #999;
  }
  .shop-tip {
    margin: 5px 0;
  }
}
.tab-content {
  width: 100%;
}
.shop-head {
  // background: white;
  background: url(./images/detail-bg.png) no-repeat;
  background-size: 100% 100%;
  padding: 10px 10px 3px;
  font-size: 14px;
  .shop-name {
    font-weight: 700;
    font-size: 18px;
    line-height: 1.2;
    margin-bottom: 8px;
    margin-right: 5px;
    height: 42px;
  }

  .shop-head__content {
    flex: 1;
  }
  .shop-logo {
    width: 90px;
    height: 70px;
    margin-right: 5px;
    border-radius: 1px;
    position: relative;

    .img-count {
      position: absolute;
      right: 5px;
      bottom: 5px;
      display: inline-block;
      width: 16px;
      height: 16px;
      background: rgba(255, 255, 255, 0.84);
      text-align: center;
      border-radius: 2px;
      line-height: 16px;
      color: rgb(56, 142, 253);
    }
  }
}
.shop-info-main {
  position: absolute;
  bottom: 0;
  width: 100%;
  z-index: 999;
  color: rgb(255, 255, 255);
  box-sizing: border-box;
  /*background: url(~@/assets/images/shop/gradient.png) center center no-repeat rgba(0, 0, 0, 0.6);
    background-size:contain;*/
  padding: 2px 10px;
  &:after {
    content: '';
    height: 0;
    display: block;
    box-shadow: 1px 1px 50px 50px rgba(0, 0, 0, 0.6);
  }
}

// .shop-address {
//   flex: 1;
//   color: #424242;
//   line-height: 1.2;
//   /*border-right: 1px solid #e4e4e4;*/
//   @include border-right('#e4e4e4', 'after');
//   font-size: 0.92em;
//   padding: 0 8px;
//   &:active {
//     background: rgba(208, 208, 208, 0.11);
//   }
// }
.shop-nav {
  padding-left: 15px;
  &::before {
    content: '\e679';
    font-family: iconfont;
    font-size: 18px;
    padding-right: 5px;
    display: block;
    color: #4e85fb;
  }
}
.shop-phone {
  padding: 0 15px 0 17px;
  font-size: 24px;
  color: #4e85fb;
  &:active {
    background: rgba(128, 128, 128, 0.1);
  }
  &:before {
    content: '\e618';
    font-family: iconfont;
    font-size: 22px;
  }
}
.comment-entrance {
  padding: 8px 10px;
  background: rgb(255, 255, 255);
  margin: 10px 0;
  border: 1px solid $border-color-gray;
  border-width: 1px 0;
  .entrance-name {
    color: #4c4c4c;
    font-weight: 400;
  }
  .entrance-note {
    color: rgb(165, 165, 165);
    font-size: 0.9em;
  }
  .entrance-actions {
    &::after {
      content: '\E605';
      font-family: iconfont;
      float: right;
      color: rgb(128, 128, 128);
    }
  }
}
.weui-cells_form {
  margin-top: -1px;
  .weui-label {
    // width: 110px;
    // font-size: 16px;
  }
}
.shop-info {
  background: white;
  border-bottom: 1px solid $border-color-gray;
  margin-bottom: 5px;
  font-size: 14px;
  .shop-about {
    padding: 0 0 8px 8px;
    /*.icon-ditu{
        font-size:18px;
      }*/
    &::before {
      /*display:none;*/
      content: '\e647';
      font-family: iconfont;
      font-size: 1.2em;
    }
  }
}
.tip-list {
  list-style: decimal;
  margin-left: 18px;
  font-size: 14px;
  line-height: 1.6;
  li {
    margin-bottom: 5px;
  }
}
svg.icon-down {
  border-radius: 50%;
  // border: 1px solid rgb(53, 53, 53);
  padding: 1px;
  line-height: 1em;
  font-size: 14px;
  float: right;
  margin-top: 5px;
}
.tab-container {
  background: white;
}

.empty-tip {
  flex: 1;
  text-align: center;
  color: gray;
  margin-top: 100px;
}
pre {
  white-space: normal;
}
.scrollable {
  overflow-y: scroll;
}
.unscrollable {
  overflow-y: hidden;
}

.select-inline {
  display: inline-block;
  position: relative;
  padding: 0 20px 0 5px;
  background: rgb(217, 234, 253);
  color: rgb(255, 255, 255);
  border-radius: 2px;
  margin-right: 5px;
  select {
    -webkit-appearance: none;
    border: 0;
    font-size: 1em;
    height: 26px;
    line-height: 26px;
    min-width: 20px;
    background: transparent;
    color: rgb(80, 149, 232);
  }
  &::after {
    content: ' ';
    display: inline-block;
    border-style: solid;
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px 5px 0;
    border-color: rgb(80, 149, 232) transparent transparent;
  }
}
.weui-input-bold {
  font-weight: 700;
  text-transform: uppercase;
  &::placeholder {
    font-weight: 400;
  }
}
@import '~styles/variable/global.scss';
.net-price {
  color: #fe861f;
  margin: 0;
  font-size: 18px;
  &::before {
    font-size: 0.7em;
    margin-right: 1px;
  }
}
.shop-price {
  margin-left: 5px;
  font-size: 12px;
  // text-decoration: line-through;
  color: gray;
  text-align: right;
}
.picker {
  position: relative;
  padding-right: 15px;
  white-space: nowrap;
  &.picker-date {
    flex: 7;
  }
  &.picker-time {
    flex: 4;
    text-align: right;
  }
  &::after {
    content: ' ';
    display: inline-block;
    border-style: solid;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px 5px 0;
    border-color: black transparent transparent;
  }
}
.shop-bottom {
  position: fixed;
  opacity: 1;
  transition: transform 100ms;
  will-change: transform;
  .price-block {
    display: flex;
    align-items: center;
  }
  .price-amount {
    font-size: 20px;
    margin-left: 10px;
    &::before {
      font-size: 0.7em;
    }
    &.price-amount-delete {
      position: relative;
      &::after {
        content: '';
        width: 100%;
        height: 1px;
        background: black;
        top: 50%;
        left: 0;
        position: absolute;
      }
    }
  }
  .vip-price-amount {
    font-size: 18px;
    color: #fe8e33;
    margin-left: 10px;
    &::before {
      font-size: 0.7em;
    }
  }
  .vip-price-icon {
    font-size: 13px;
    color: white;
    background: #fe8e33;
    padding: 2px;
    border-radius: 2px;
    height: 14px;
    line-height: 14px;
    margin-left: 2px;
    white-space: nowrap;
  }
  &.shop-bottom-hidden {
    /*
      visibility: hidden;
      overflow:hidden;
      */
    transform: translate3d(0, 100%, 0);
  }
}
.shop-home-view {
  .weui-actionsheet__cell > a {
    display: block;
    color: inherit;
  }
  .service-empty {
    padding: 20px;
  }
}
.touchmove-overlay {
  position: absolute;
  z-index: 1;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
}
.bottom-left {
  flex: 1;
  padding-left: 5px;
}
.bottom-right {
  width: 100px;
  line-height: 47px;
  text-align: center;
  // background: rgb(78, 133, 251);
  color: white;
  &.disabled {
    background: #cacaca;
    &:active {
      background: #cacaca;
    }
  }
  // &:active{
  //   background-color: rgba(78, 133, 251, 0.8);
  // }
}
.customer-service {
  width: 80px;
  line-height: 1;
  text-align: center;
  justify-content: center;
  align-items: center;
  position: relative;
  .icon-service {
    font-size: 20px;
    color: #3c8cf8;
  }
  &::before {
    content: '';
    height: 100%;
    width: 1px;
    background: rgb(230, 230, 230);
    left: 0;
    top: 0;
    position: absolute;
    transform: scale(0.7);
  }
  text-align: center;
  > p {
    font-size: 12px;
    margin-top: 5px;
    color: rgb(86, 86, 86);
  }
  &:active {
    background-color: rgba(78, 133, 251, 0.8);
  }
}
.reserve-tip {
  text-align: center;
  margin: 10px 0;
  font-size: 14px;
  > b {
    color: #388efd;
    margin: 2px;
  }
}

.detail-header {
  background: url(./images/nav-header-detail.png) no-repeat;
  background-size: 100% 100%;
}
.b-content {
  padding: 0 15px;
}
.header-box {
  position: relative;
  .h-content {
    display: flex;
    padding: 15px 15px 0 30px;
  }

  .bg-color {
    position: absolute;
    width: 100%;
    background: url(./images/detail-bg.png) no-repeat;
    background-size: 100% 100%;
    top: 0;
    height: 55px;
  }

  // padding: 15px;
  // margin: 0 15px;
  .shop-logo {
    width: 100px;
    height: 100px;
    border-radius: 10px;
    margin-right: 14px;
    position: relative;
    .img-count {
      position: absolute;
      right: 5px;
      bottom: 5px;
      display: inline-block;
      width: 15px;
      height: 15px;
      background: rgba(0, 0, 0, 0.6);
      text-align: center;
      border-radius: 3px;
      line-height: 15px;
      font-size: 11px;
      color: #fff;
    }
  }
  .right {
    .shop-name {
      font-size: 16px;
      font-weight: bold;
      color: #111111;
    }
    .rater-box {
      color: #fd4925;
      font-size: 12px;
      .c-score {
        margin-left: 5px;
      }
    }
  }
}
.shop-info-box {
  background: #fff;
  border-radius: 10px;
  padding: 44px 15px 15px 15px;
  margin-top: -29px;
  margin-bottom: 10px;
  .shop-time {
    color: #111;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 10px;
    .icon-common {
      font-size: 14px;
      font-weight: normal;
      margin-right: 6px;
    }
  }
  .shop-address {
    // {{ AURA-X: Modify - 优化地址显示和按钮样式. Confirmed via 寸止 }}
    padding: 12px 0;

    .address-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }

    .address-text {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      line-height: 1.4;

      .address-icon {
        font-size: 16px;
        color: #fd4925;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .address-label {
        flex: 1;
        word-break: break-all;
      }
    }

    .action-buttons {
      display: flex;
      gap: 12px;
      margin-left: 16px;
      flex-shrink: 0;
    }

    .action-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-width: 48px;
      min-height: 48px;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.95);
        background: #e9ecef;
      }

      .btn-icon {
        width: 24px;
        height: 24px;
        font-size: 16px;
        color: #fd4925;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .btn-text {
        font-size: 11px;
        color: #666;
        font-weight: 500;
        line-height: 1;
      }

      &.navigation-btn:hover {
        background: #fff5f4;

        .btn-icon {
          color: #e63946;
        }
      }

      &.phone-btn:hover {
        background: #f0f9ff;

        .btn-icon {
          color: #0284c7;
        }

        .btn-text {
          color: #0284c7;
        }
      }
    }
  }
}
.vip-banner-image {
  border-radius: 10px;
  margin-bottom: 10px;
}
</style>

<template>
  <container
    class="shop-detail"
    @ready="init"
    @leave="onLeave"
    @resume="onResume"
    :keep-alive="keepAlive"
  >
    <x-header ref="header" title="门店详情" class="detail-header">
      <x-button slot="left" type="back"></x-button>
      <div v-if="isReady" slot="right">
        <div slot="right" class="share">
          <i class="icon_jglh icon-fenxiang1" @click="share"></i>
        </div>
      </div>
    </x-header>
    <content-view
      class="shop-inspection"
      ref="view"
      :status="status"
      @reload="reload"
      @scroll-bottom="doCommentsAction('loadmore')"
    >
      <template v-if="status == AppStatus.READY">
        <div class="header-box">
          <div class="bg-color"></div>
          <div class="h-content">
            <biz-image
              class="shop-logo"
              :src="shop.logo"
              @click="playShopPhotos(0)"
            >
              <span class="img-count">{{ shop.imagesArray.length }}</span>
            </biz-image>
            <div class="right">
              <h4 class="shop-name">{{ shop.name }}</h4>
              <div class="rater-box">
                <template
                  v-if="shop.commentCount != 0 && shop.commentScore != 0"
                >
                  <van-icon name="star" color="#fd4925" size="13px" />
                  <span class="c-score">{{
                    shop.commentScore.toFixed(1)
                  }}</span>
                </template>
                <span v-else>暂无评分</span>
              </div>
            </div>
          </div>
        </div>
        <div class="b-content">
          <div class="shop-info-box">
            <div class="shop-time">
              <i class="icon_jglh icon-a-sc-dengdaishijian icon-common"></i>
              <span>营业时段：</span>
              <span>{{ serviceTime.start }} ~ {{ serviceTime.end }}</span>
            </div>
            <div class="shop-address">
              <div class="box">
                <span class="address"
                  ><i
                    class="icon_jglh icon-a-sc-weizhidizhidingwei icon-common"
                  ></i
                  >{{ shop.address }}</span
                >
                <div class="btn">
                  <div class="navigation" @click="goNavigate">
                    <i class="icon_jglh icon-sc-daohang icon"></i>
                    <span>导航</span>
                  </div>
                  <div class="phone" @click="showContacts">
                    <i class="icon_jglh icon-sc-dianhua icon"></i>
                    <span>电话</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- <div class="shop-head flex-row">
            <div class="shop-head__content">
              <h4 class="shop-name">{{ shop.name }}</h4>
              <div class="shop-time">
                <span>营业时段：</span>
                <span>{{ serviceTime.start }} ~ {{ serviceTime.end }}</span>
              </div>
            </div>
            <biz-image
              class="shop-logo"
              :src="shop.logo"
              @click="playShopPhotos(0)"
            >
              <span class="img-count">{{ shop.imagesArray.length }}</span>
            </biz-image>
          </div>
          <div class="shop-info">
            <div class="shop-about flex-row center">
              <div class="shop-address" @click="goNavigate">
                {{ shop.address }}
              </div>
              <a class="shop-phone" @click="showContacts"></a>
            </div>
          </div> -->
          <!-- <biz-image
            class="vip-banner-image"
            src="FrvXMUkDlFXuv2tnmkCGtauGN50Q"
            @click="$_route_vipPage('car')"
            type="?imageView2/1/format/jpg/q/90"
            fill="fill"
            :immediate="true"
            v-if="!$_auth_isCarVip"
          >
          </biz-image> -->

          <!-- <vip-banner
            class="vip-banner"
            style="margin:0 0 5px;"
            :vip="$_auth_isCarVip"
            @click="$_route_vipPage('car')"
            fill="contain"
            :banners="[
              require('@pkg/vehicle-business/assets/images/vip0-banner.png'),
              require('@pkg/vehicle-business/assets/images/vip1-banner.png')
            ]"
          ></vip-banner> -->
          <!-- <vip-banner style="margin:0 0 5px;" :vip="$_auth_isCarVip" @click="$_route_vipPage"></vip-banner> -->

          <div class="shop-services flex-col">
            <tabs
              class="tabs"
              ref="tabs"
              data-role="scroll-head"
              v-model="viewType"
              :tabs="viewTypes"
              :sticky="true"
            >
            </tabs>

            <div
              class="flex-row slider"
              style="flex: 1"
              data-role="scroll-body"
            >
              <div
                v-show="viewType === ViewType.A"
                ref="scroll-body__service"
                class="tab-content"
                style="flex: 1"
              >
                <service-card
                  :category="category"
                  :list="shop.businessOwnCarServiceCategories"
                  @click="goForReserve"
                  :disabled="shop.acceptStatus != 1"
                >
                </service-card>
                <flow-path-problem></flow-path-problem>
              </div>
              <div
                v-show="viewType === ViewType.B"
                ref="scroll-body__comments"
                data-sticky="true"
                class="tab-content service-rules"
              >
                <shop-comments
                  ref="comments"
                  :scroll-target="$refs['view'].getScroller()"
                  :id="comments.shopId"
                  :type="comments.type"
                >
                </shop-comments>
              </div>
            </div>
          </div>

          <transition
            name="actionsheet"
            class="weui-skin_android"
            @enter="
              () => {
                this.showShopContact = true;
              }
            "
            @beforeLeave="
              () => {
                this.showShopContact = false;
              }
            "
          >
            <router-view> </router-view>
          </transition>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import {
  getAppURL,
  formatDate,
  fixRichHtmlImageSize,
  formatShopHours,
  dateCompare,
} from '@/utils';
import { getImageURL } from '@/common/image';
import { AppStatus, ImageType } from '@/enums';
import { playPhotos, navigate } from '@/bridge';
import { mixinScroll, mixinAuthRouter, mixinShare } from '@/mixins';
import { dialog, loading, toast } from '@/bus';
import { checkServiceStatus, ServiceEnum } from '@/utils/maintenance';
import ShopComments from '@pkg/vehicle-business/components/_InspectionCommentList.vue';
import VipBanner from '@pkg/vehicle-business/components/_VipBanner.vue';
import ServiceCard from './_ServiceCard.vue';
import FlowPathProblem from './_FlowPathProblem.vue';
import {
  getInspectionShopInfo,
  getInspectionReserveCount,
} from '@pkg/vehicle-business/api';
import { Icon } from 'vant';
const ViewType = {
  A: 0,
  B: 1,
};

function transfromShopCategory(value) {
  if (value === 'ccb') return 9;
  return value;
}

// 代办审车商家id
const AGENT_INSPECTION_SHOP_ID = -2;

function getDefaultTime() {
  const now = new Date();
  const hour = now.getHours() + 1 > 18 ? 10 : now.getHours() + 1;
  const date = now.getHours() + 1 > 18 ? now.getDate() + 1 : now.getDate();
  const dateTime = new Date(
    now.getFullYear(),
    now.getMonth(),
    date,
    hour,
    0,
    0
  );
  return dateTime;
}

export default {
  name: 'OnlineInspectionShop',
  components: {
    ShopComments,
    'service-card': ServiceCard,
    'flow-path-problem': FlowPathProblem,
    [Icon.name]: Icon,
    VipBanner,
  },
  mixins: [mixinScroll, mixinAuthRouter, mixinShare],
  data() {
    const now = new Date();
    const query = this.$route.query;
    const shopCategory = transfromShopCategory(query.category);
    return {
      AppStatus,
      ViewType,
      ImageType,
      status: AppStatus.LOADING,
      pageActive: true,
      panelHeight: 'auto',
      hideBuyButton: false,
      showShopContact: false,
      reachBottom: false,
      servicePhone: '************',
      shop: {},
      counts: 0,
      viewType: ViewType.A,
      comments: {
        shopId: 0,
        type: 'wash',
      },
      category: shopCategory,
      form: {
        agree: false,

        inspector: '',
        car_model: '',
        name: '',
        phone: '',
        number: '',
        id_number: '',
        date: now.getTime() + 1000 * 60 * 60 * 24,
        time: getDefaultTime(),
      },
      viewTypes: [
        {
          name: '审车服务',
          value: ViewType.A,
        },
        {
          name: '用户评价',
          value: ViewType.B,
        },
      ],
    };
  },
  mounted() {
    this.$_auth_checkSession(true);
  },
  computed: {
    isReady() {
      return this.status == AppStatus.READY;
    },
    // 商家营业时间，字段值为特殊格式，需要转换
    serviceTime() {
      const shop = this.shop;
      const start = formatShopHours(shop.serviceTimeS);
      const end = formatShopHours(shop.serviceTimeE);
      return {
        start,
        end,
      };
    },
    couldUseVipPrice() {
      return (
        this.$_auth_isCarVip &&
        this.shop.vipPrice > 0 &&
        this.shop.price > this.shop.vipPrice
      );
    },
    myPrice() {
      return function (item) {
        if (this.$_auth_isCarVip) return item.vipPrice;
        return item.price;
      };
    },
    shareInfo() {
      const title = '我正在这家检测站审车，审的很快服务很好，推荐！';
      const desc = this.shop.name;
      const logo = getImageURL(this.shop.logo, '_xs');
      const url = getAppURL(this.$route.fullPath);
      const shareInfo = {
        title: title,
        desc: desc,
        imgUrl: logo,
        link: url,
      };
      return shareInfo;
    },
  },
  watch: {
    viewType(val) {
      if (val === ViewType.B) {
        this.doCommentsAction('init');
      }
    },
  },
  methods: {
    ...{ formatDate, dateCompare },
    goForReserve(item) {
      // // 暂时设置
      // if (this.checkTimeTemp()) return;
      const id = this.$route.params.id;
      this.$_auth_push({
        name: 'OnlineInspectionReserve',
        params: {
          id: id,
          price: this.myPrice(item),
          shop: this.shop,
          catId: item.catId,
          catName: item.catName,
          title: item.catName,
        },
        query: this.$route.query,
      });
    },
    checkTimeTemp() {
      const { available, message } = checkServiceStatus(
        ServiceEnum.CAR_INSPECTION
      );
      if (!available) {
        dialog().alert(message, {
          title: '通知',
        });
      }
      return !available;
    },
    onLeave() {
      this.status = AppStatus.LOADING;
    },
    onResume() {
      // this.status = AppStatus.READY;
    },
    getPageData() {
      const shopId = this.$route.params.id;
      this.comments.shopId = shopId;
      getInspectionShopInfo(shopId, this.category).then(
        shop => {
          if (!(shop.images instanceof Array) && shop.images) {
            shop.images = shop.images.split(',');
          }
          this.shop = shop;
          // 显示评价条数
          this.viewTypes[1].name =
            shop.commentCount > 0
              ? shop.commentCount < 1000 > 0
                ? `用户评价(${shop.commentCount})`
                : '用户评价(999+)'
              : '用户评价';
          // 若有车牌号，自动填充车牌号
          const car = this.$route.query.car;
          if (car) {
            this.form.number = car;
          }
          this.updateShare();
          this.status = AppStatus.READY;
        },
        err => {
          this.status = AppStatus.ERROR;
          console.error(err);
        }
      );
    },
    init() {
      this.getPageData();
    },
    reload() {
      this.status = AppStatus.LOADING;
      this.getPageData();
    },
    goNavigate() {
      const { address, lat, lng, name } = this.shop;
      navigate({
        address,
        name: name,
        longitude: lng,
        latitude: lat,
        callback() {},
      });
    },
    updateShare() {
      this.$_share_update(this.shareInfo);
    },
    share() {
      this.$_share(this.shareInfo);
    },
    showContacts() {
      const phoneList = this.shop.telePhone.split(',').map((item, index) => {
        return {
          name: '',
          value: item,
        };
      });
      this.$router.push({
        name: 'inspector/contacts',
        params: {
          title: '审车小秘书',
          platform: false,
          list: phoneList,
        },
      });
    },
    submit() {
      const that = this;
    },
    playPhotos(images, initIndex = 0) {
      const photos = images.map(function (item, i) {
        return {
          title: `图片${i}`,
          url: getImageURL(item, ImageType.MEDIUM),
        };
      });
      const option = {
        download: true,
        initIndex,
        photos,
      };
      playPhotos(option);
    },
    playShopPhotos(index) {
      this.playPhotos(this.shop.images, index);
    },
    setActiveService(item) {
      this.activeCategory = item;
    },
    setItem(item) {
      this.checkedService = item;
    },
    doCommentsAction(action) {
      if (this.$refs.comments) {
        if (action === 'init') {
          if (this.$refs.comments.loaded) return;
          this.$refs.comments.init();
          this.$refs.comments.loaded = true;
        } else if (action === 'loadmore') {
          console.log('loadmore...');
          this.$refs.comments.loadMore();
        }
      }
    },
  },
};
</script>
