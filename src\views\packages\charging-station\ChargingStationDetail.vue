<template>
  <container class="charging-station-detail">
    <x-header :title="'充电站详情'">
      <x-button slot="left" type="back"></x-button>
      <x-button slot="right" type="text" @click="share">
        <van-icon name="share" />
      </x-button>
    </x-header>

    <content-view
      :status="status"
      :refresh-action="refreshAction"
      @refresh="refresh"
      @reload="reload"
    >
      <template v-if="status == AppStatus.READY">
        <div class="station-page">
          <!-- 顶部图片 -->
          <div class="station-photos">
            <div class="photo">场站照片维护中</div>
            <div class="photo">场站照片维护中</div>
          </div>

          <!-- 站点信息卡片 -->
          <div class="station-info">
            <h2 class="title">郑州北三环科华超充充电站</h2>
            <div class="sub-info">
              <span class="score">4.8分</span>
              <span class="success">刚刚有人成功充过电</span>
            </div>
            <div class="tags">
              <span class="tag">特来电权益</span>
              <span class="tag">自营</span>
              <span class="tag">对外开放</span>
              <span class="tag">露天1F</span>
              <span class="tag">PLUS会员</span>
              <span class="tag">超级快充</span>
              <span class="tag">近期最大443kW</span>
            </div>
            <div class="address">
              河南省郑州市金水区北三环经三路西南角
              <span class="nav">导航</span>
            </div>
          </div>

          <!-- 服务设施 -->
          <!-- <div class="services">
            <div class="service-item"><i class="icon">🅿️</i> 专用通道</div>
            <div class="service-item"><i class="icon">☕</i> 休息室</div>
            <div class="service-item"><i class="icon">💡</i> 场站照明</div>
          </div> -->

          <!-- 充电桩信息 -->
          <div class="charge-info">
            <div class="charge-box">
              <div class="charge-item super">
                <div class="type">超充</div>
                <div class="status">空闲 0/2</div>
                <div class="power">最大功率 480kW</div>
              </div>
              <div class="charge-item fast">
                <div class="type">快充</div>
                <div class="status">空闲 11/22</div>
                <div class="power">最大功率 240kW</div>
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="price-info">
            <h3>价格信息</h3>
            <div class="current-price">
              <span class="value">0.9000</span><span class="unit">元/度</span>
            </div>
            <div class="time-price">
              16:00开始 1.2100元/度 <span class="more">全部时段 ></span>
            </div>
            <div class="parking">
              🅿️ 停车参考价：<span class="free">停车免费</span>
            </div>
          </div>
        </div>
      </template>
    </content-view>
  </container>
</template>

<script>
import { mixinAuthRouter } from '@/mixins';
import { AppStatus } from '@/enums';
import { toast } from '@/bus';
import { getChargingStationDetail } from '@/api/modules/charging-station';
import { Icon, Tag, Rate, Button } from 'vant';
import {
  getStatusText,
  getTagType,
  getServiceIcon,
  generateNavigationUrl,
} from './utils';
export default {
  name: 'StationPage',
  components: {
    [Icon.name]: Icon,
    [Tag.name]: Tag,
    [Rate.name]: Rate,
    [Button.name]: Button,
  },
  mixins: [mixinAuthRouter],
  data() {
    return {
      AppStatus,
      status: AppStatus.LOADING,
      refreshAction: 0,
      station: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      const { id } = this.$route.params;
      if (!id) {
        toast().tip('充电站ID不能为空');
        this.$_router_back();
        return;
      }
      this.getStationDetail(id);
    },

    // 获取充电站详情
    getStationDetail(stationId) {
      this.status = AppStatus.LOADING;
      getChargingStationDetail(stationId)
        .then(res => {
          this.station = res;
          this.status = AppStatus.READY;
        })
        .catch(e => {
          console.error(e);
          this.status = AppStatus.ERROR;
          toast().tip(e);
        });
    },

    // 刷新
    refresh() {
      const { id } = this.$route.params;
      this.getStationDetail(id);
      this.refreshAction = Date.now();
    },

    // 重新加载
    reload() {
      this.init();
    },

    // 使用工具函数
    getStatusText,
    getTagType,
    getServiceIcon,

    // 拨打电话
    callPhone() {
      if (this.station.phone) {
        window.location.href = `tel:${this.station.phone}`;
      }
    },

    // 导航到充电站
    navigateToStation() {
      try {
        if (window.jsBridge && window.jsBridge.openMap) {
          window.jsBridge.openMap({
            latitude: this.station.lat,
            longitude: this.station.lng,
            name: this.station.name,
            address: this.station.address,
          });
        } else {
          const url = generateNavigationUrl(
            this.station.lng,
            this.station.lat,
            this.station.name
          );
          window.open(url);
        }
      } catch (error) {
        console.error('导航失败:', error);
        toast().tip('导航功能暂时不可用');
      }
    },

    // 分享
    share() {
      if (navigator.share) {
        navigator.share({
          title: this.station.name,
          text: `${this.station.name} - ${this.station.address}`,
          url: window.location.href,
        });
      } else {
        // 复制链接到剪贴板
        if (navigator.clipboard) {
          navigator.clipboard.writeText(window.location.href);
          toast().tip('链接已复制到剪贴板');
        } else {
          toast().tip('分享功能暂不支持');
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.station-page {
  background: #f5f6fa;
  min-height: 100%;
  color: #333;

  .station-photos {
    display: flex;
    position: relative;
    background: #eef2f7;
    padding: 8px;

    .photo {
      flex: 1;
      height: 120px;
      background: #d9d9d9;
      margin-right: 8px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      color: #666;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .station-info {
    background: #fff;
    margin: 10px;
    padding: 12px;
    border-radius: 8px;

    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }

    .sub-info {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;

      .score {
        margin-right: 10px;
      }
      .success {
        color: #4caf50;
      }
    }

    .tags {
      margin-bottom: 10px;
      .tag {
        display: inline-block;
        background: #f2f2f2;
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 12px;
        margin: 2px 4px 2px 0;
      }
    }

    .address {
      font-size: 13px;
      color: #555;

      .nav {
        float: right;
        color: #1989fa;
        font-size: 14px;
      }
    }
  }

  .services {
    background: #fff;
    margin: 0 10px 10px;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    justify-content: space-around;
    font-size: 13px;

    .service-item {
      display: flex;
      align-items: center;
      .icon {
        margin-right: 4px;
      }
    }
  }

  .charge-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 10px;
    border-radius: 8px;

    .highlight {
      font-size: 13px;
      color: #ff4081;
      margin-bottom: 10px;
    }

    .charge-box {
      display: flex;
      justify-content: space-between;

      .charge-item {
        flex: 1;
        padding: 12px;
        border-radius: 8px;
        font-size: 13px;

        .type {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 4px;
        }
        .status {
          margin-bottom: 4px;
        }
      }

      .super {
        background: #ffe6eb;
        margin-right: 8px;
        .type {
          color: #ff3366;
        }
      }

      .fast {
        background: #fff3e0;
        .type {
          color: #ff9800;
        }
      }
    }
  }

  .price-info {
    background: #fff;
    margin: 0 10px 10px;
    padding: 12px;
    border-radius: 8px;

    h3 {
      font-size: 14px;
      margin-bottom: 8px;
    }

    .current-price {
      font-size: 18px;
      color: #ff3300;
      font-weight: bold;

      .value {
        font-size: 20px;
      }
      .unit {
        font-size: 13px;
        margin-left: 4px;
      }
    }

    .time-price {
      margin-top: 6px;
      font-size: 13px;
      color: #666;

      .more {
        float: right;
        color: #1989fa;
      }
    }

    .parking {
      margin-top: 10px;
      font-size: 13px;
      .free {
        color: #4caf50;
        margin-left: 4px;
      }
    }
  }
}
</style>
